package financialsheet

import (
	"context"
	"testing"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/model/league"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// MockLeagueService for testing - only implements the method we need
type MockLeagueService struct {
	mock.Mock
}

func (m *MockLeagueService) RecordTransactionForAllUserLeagues(ctx context.Context, userID string, transactionDate time.Time) error {
	args := m.Called(ctx, userID, transactionDate)
	return args.Error(0)
}

// Implement other required methods as no-ops for testing
func (m *MockLeagueService) CreateLeague(ctx context.Context, ownerUserID string, ownerUserName string, ownerPhotoURL string, leagueName string, startDate time.Time, endDate time.Time) (*league.League, error) {
	return nil, nil
}

func (m *MockLeagueService) FindLeague(ctx context.Context, leagueID string, userID string) (*league.League, error) {
	return nil, nil
}

func (m *MockLeagueService) FindAllLeagues(ctx context.Context, userID string) ([]*league.League, error) {
	return nil, nil
}

func (m *MockLeagueService) PatchLeague(ctx context.Context, leagueID string, userID string, name *string) (*league.League, error) {
	return nil, nil
}

func (m *MockLeagueService) DeleteLeague(ctx context.Context, leagueID string, userID string) error {
	return nil
}

func (m *MockLeagueService) InviteDetails(ctx context.Context, code string) (*league.League, error) {
	return nil, nil
}

func (m *MockLeagueService) JoinLeague(ctx context.Context, userID string, userName string, photoURL string, inviteCode string) (*league.League, error) {
	return nil, nil
}

func (m *MockLeagueService) LeaveLeague(ctx context.Context, leagueID string, userID string) error {
	return nil
}

func (m *MockLeagueService) StartNewSeasonInLeague(ctx context.Context, leagueID string, userID string, startDate time.Time, endDate time.Time) (*league.League, error) {
	return nil, nil
}

func (m *MockLeagueService) FindLeagueRanking(ctx context.Context, leagueID string, userID string) (*league.LeagueRanking, error) {
	return nil, nil
}

func (m *MockLeagueService) FindLeagueCard(ctx context.Context, leagueID string, userID string) (*league.LeagueCard, error) {
	return nil, nil
}

func (m *MockLeagueService) FindAllLeaguesCards(ctx context.Context, userID string) ([]*league.LeagueCard, error) {
	return nil, nil
}

func TestNoTransactions_Success_FirstTime(t *testing.T) {
	// Arrange
	ctx := context.Background()
	mockRepo := &MockRepository{}
	mockLeague := &MockLeagueService{}

	service := &service{
		Repository:    mockRepo,
		LeagueService: mockLeague,
	}

	record := &financialsheet.Record{
		ObjectID: primitive.NewObjectID(),
		UserID:   "user123",
		UserName: "Test User",
		Points: financialsheet.Points{
			Current:             0,
			Best:                0,
			LastTransactionDate: time.Time{}, // Zero time - first time
		},
		YearData: make(map[int]financialsheet.YearData),
		Balance:  monetary.Amount(0),
	}

	// Mock expectations
	mockRepo.On("Update", ctx, mock.AnythingOfType("*financialsheet.Record")).Return(nil)
	mockLeague.On("RecordTransactionForAllUserLeagues", ctx, "user123", mock.AnythingOfType("time.Time")).Return(nil)

	// Act
	err := service.NoTransactions(ctx, record, time.UTC)

	// Assert
	assert.NoError(t, err)
	assert.Equal(t, 1, record.Points.Current)
	assert.Equal(t, 1, record.Points.Best)
	assert.False(t, record.Points.LastTransactionDate.IsZero())
	assert.False(t, record.Points.LastNoTransactionDate.IsZero())
	assert.Len(t, record.Points.NoTransactionDates, 1)

	mockRepo.AssertExpectations(t)
	mockLeague.AssertExpectations(t)
}

func TestNoTransactions_Success_ContinueStreak(t *testing.T) {
	// Arrange
	ctx := context.Background()
	mockRepo := &MockRepository{}
	mockLeague := &MockLeagueService{}

	service := &service{
		Repository:    mockRepo,
		LeagueService: mockLeague,
	}

	yesterday := time.Now().AddDate(0, 0, -1)
	yesterdayNormalized := time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 0, 0, 0, 0, yesterday.Location())

	record := &financialsheet.Record{
		ObjectID: primitive.NewObjectID(),
		UserID:   "user123",
		UserName: "Test User",
		Points: financialsheet.Points{
			Current:             5,
			Best:                10,
			LastTransactionDate: yesterdayNormalized,
		},
		YearData: make(map[int]financialsheet.YearData),
		Balance:  monetary.Amount(0),
	}

	// Mock expectations
	mockRepo.On("Update", ctx, mock.AnythingOfType("*financialsheet.Record")).Return(nil)
	mockLeague.On("RecordTransactionForAllUserLeagues", ctx, "user123", mock.AnythingOfType("time.Time")).Return(nil)

	// Act
	err := service.NoTransactions(ctx, record, yesterday.Location())

	// Assert
	assert.NoError(t, err)
	assert.Equal(t, 6, record.Points.Current) // Streak should continue
	assert.Equal(t, 10, record.Points.Best)   // Best should remain the same

	mockRepo.AssertExpectations(t)
	mockLeague.AssertExpectations(t)
}

func TestNoTransactions_Success_NewBestStreak(t *testing.T) {
	// Arrange
	ctx := context.Background()
	mockRepo := &MockRepository{}
	mockLeague := &MockLeagueService{}

	service := &service{
		Repository:    mockRepo,
		LeagueService: mockLeague,
	}

	yesterday := time.Now().AddDate(0, 0, -1)
	yesterdayNormalized := time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 0, 0, 0, 0, yesterday.Location())

	record := &financialsheet.Record{
		ObjectID: primitive.NewObjectID(),
		UserID:   "user123",
		UserName: "Test User",
		Points: financialsheet.Points{
			Current:             10,
			Best:                10,
			LastTransactionDate: yesterdayNormalized,
		},
		YearData: make(map[int]financialsheet.YearData),
		Balance:  monetary.Amount(0),
	}

	// Mock expectations
	mockRepo.On("Update", ctx, mock.AnythingOfType("*financialsheet.Record")).Return(nil)
	mockLeague.On("RecordTransactionForAllUserLeagues", ctx, "user123", mock.AnythingOfType("time.Time")).Return(nil)

	// Act
	err := service.NoTransactions(ctx, record, yesterday.Location())

	// Assert
	assert.NoError(t, err)
	assert.Equal(t, 11, record.Points.Current) // Streak should continue
	assert.Equal(t, 11, record.Points.Best)    // Best should be updated

	mockRepo.AssertExpectations(t)
	mockLeague.AssertExpectations(t)
}

func TestNoTransactions_Success_ResetStreak(t *testing.T) {
	// Arrange
	ctx := context.Background()
	mockRepo := &MockRepository{}
	mockLeague := &MockLeagueService{}

	service := &service{
		Repository:    mockRepo,
		LeagueService: mockLeague,
	}

	threeDaysAgo := time.Now().AddDate(0, 0, -3)
	threeDaysAgoNormalized := time.Date(threeDaysAgo.Year(), threeDaysAgo.Month(), threeDaysAgo.Day(), 0, 0, 0, 0, threeDaysAgo.Location())

	record := &financialsheet.Record{
		ObjectID: primitive.NewObjectID(),
		UserID:   "user123",
		UserName: "Test User",
		Points: financialsheet.Points{
			Current:             5,
			Best:                10,
			LastTransactionDate: threeDaysAgoNormalized,
		},
		YearData: make(map[int]financialsheet.YearData),
		Balance:  monetary.Amount(0),
	}

	// Mock expectations
	mockRepo.On("Update", ctx, mock.AnythingOfType("*financialsheet.Record")).Return(nil)
	mockLeague.On("RecordTransactionForAllUserLeagues", ctx, "user123", mock.AnythingOfType("time.Time")).Return(nil)

	// Act
	err := service.NoTransactions(ctx, record, threeDaysAgo.Location())

	// Assert
	assert.NoError(t, err)
	assert.Equal(t, 1, record.Points.Current)  // Streak should reset
	assert.Equal(t, 10, record.Points.Best)    // Best should remain the same
	assert.Len(t, record.Points.MissedDays, 1) // Should have one missed day

	mockRepo.AssertExpectations(t)
	mockLeague.AssertExpectations(t)
}

func TestNoTransactions_Error_NilRecord(t *testing.T) {
	// Arrange
	ctx := context.Background()
	service := &service{}

	// Act
	err := service.NoTransactions(ctx, nil, time.UTC)

	// Assert
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "invalid record")
}

func TestNoTransactions_Error_AlreadyMarkedToday(t *testing.T) {
	// Arrange
	ctx := context.Background()
	service := &service{}

	today := time.Now()
	todayNormalized := time.Date(today.Year(), today.Month(), today.Day(), 0, 0, 0, 0, today.Location())

	record := &financialsheet.Record{
		ObjectID: primitive.NewObjectID(),
		UserID:   "user123",
		UserName: "Test User",
		Points: financialsheet.Points{
			Current:               5,
			Best:                  10,
			LastTransactionDate:   todayNormalized,
			LastNoTransactionDate: todayNormalized, // Already marked today
		},
		YearData: make(map[int]financialsheet.YearData),
		Balance:  monetary.Amount(0),
	}

	// Act
	err := service.NoTransactions(ctx, record, today.Location())

	// Assert
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "no transactions already marked for today")
}

func TestNoTransactions_Error_AlreadyMarkedInList(t *testing.T) {
	// Arrange
	ctx := context.Background()
	service := &service{}

	today := time.Now()
	todayNormalized := time.Date(today.Year(), today.Month(), today.Day(), 0, 0, 0, 0, today.Location())

	record := &financialsheet.Record{
		ObjectID: primitive.NewObjectID(),
		UserID:   "user123",
		UserName: "Test User",
		Points: financialsheet.Points{
			Current:             5,
			Best:                10,
			LastTransactionDate: todayNormalized.AddDate(0, 0, -1),
			NoTransactionDates:  []time.Time{todayNormalized}, // Already in the list
		},
		YearData: make(map[int]financialsheet.YearData),
		Balance:  monetary.Amount(0),
	}

	// Act
	err := service.NoTransactions(ctx, record, today.Location())

	// Assert
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "no transactions already marked for this date")
}

func TestNoTransactions_Error_RepositoryFailure(t *testing.T) {
	// Arrange
	ctx := context.Background()
	mockRepo := &MockRepository{}
	mockLeague := &MockLeagueService{}

	service := &service{
		Repository:    mockRepo,
		LeagueService: mockLeague,
	}

	record := &financialsheet.Record{
		ObjectID: primitive.NewObjectID(),
		UserID:   "user123",
		UserName: "Test User",
		Points: financialsheet.Points{
			Current:             0,
			Best:                0,
			LastTransactionDate: time.Time{},
		},
		YearData: make(map[int]financialsheet.YearData),
		Balance:  monetary.Amount(0),
	}

	// Mock expectations - repository fails
	mockRepo.On("Update", ctx, mock.AnythingOfType("*financialsheet.Record")).Return(errors.New(errors.Repository, "database error", errors.Internal, nil))

	// Act
	err := service.NoTransactions(ctx, record, time.UTC)

	// Assert
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "database error")

	mockRepo.AssertExpectations(t)
}

func TestNoTransactions_Success_LeagueServiceFailure(t *testing.T) {
	// Arrange
	ctx := context.Background()
	mockRepo := &MockRepository{}
	mockLeague := &MockLeagueService{}

	service := &service{
		Repository:    mockRepo,
		LeagueService: mockLeague,
	}

	record := &financialsheet.Record{
		ObjectID: primitive.NewObjectID(),
		UserID:   "user123",
		UserName: "Test User",
		Points: financialsheet.Points{
			Current:             0,
			Best:                0,
			LastTransactionDate: time.Time{},
		},
		YearData: make(map[int]financialsheet.YearData),
		Balance:  monetary.Amount(0),
	}

	// Mock expectations - league service fails but should not affect the operation
	mockRepo.On("Update", ctx, mock.AnythingOfType("*financialsheet.Record")).Return(nil)
	mockLeague.On("RecordTransactionForAllUserLeagues", ctx, "user123", mock.AnythingOfType("time.Time")).Return(errors.New(errors.Service, "league error", errors.Internal, nil))

	// Act
	err := service.NoTransactions(ctx, record, time.UTC)

	// Assert
	assert.NoError(t, err) // Should succeed despite league service failure
	assert.Equal(t, 1, record.Points.Current)
	assert.Equal(t, 1, record.Points.Best)

	mockRepo.AssertExpectations(t)
	mockLeague.AssertExpectations(t)
}

func TestValidateNoTransactionRequest_Success(t *testing.T) {
	// Arrange
	service := &service{}
	today := time.Now()
	todayNormalized := time.Date(today.Year(), today.Month(), today.Day(), 0, 0, 0, 0, today.Location())

	record := &financialsheet.Record{
		Points: financialsheet.Points{
			LastTransactionDate:   todayNormalized.AddDate(0, 0, -1), // Yesterday
			LastNoTransactionDate: time.Time{},                       // Never marked
			NoTransactionDates:    []time.Time{},                     // Empty list
		},
	}

	// Act
	err := service.validateNoTransactionRequest(record, todayNormalized)

	// Assert
	assert.NoError(t, err)
}

func TestValidateNoTransactionRequest_Error_SameDayAsLastNoTransaction(t *testing.T) {
	// Arrange
	service := &service{}
	today := time.Now()
	todayNormalized := time.Date(today.Year(), today.Month(), today.Day(), 0, 0, 0, 0, today.Location())

	record := &financialsheet.Record{
		Points: financialsheet.Points{
			LastNoTransactionDate: todayNormalized, // Already marked today
		},
	}

	// Act
	err := service.validateNoTransactionRequest(record, todayNormalized)

	// Assert
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "no transactions already marked for today")
}

func TestValidateNoTransactionRequest_Error_DateInNoTransactionsList(t *testing.T) {
	// Arrange
	service := &service{}
	today := time.Now()
	todayNormalized := time.Date(today.Year(), today.Month(), today.Day(), 0, 0, 0, 0, today.Location())

	record := &financialsheet.Record{
		Points: financialsheet.Points{
			NoTransactionDates: []time.Time{todayNormalized}, // Already in list
		},
	}

	// Act
	err := service.validateNoTransactionRequest(record, todayNormalized)

	// Assert
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "no transactions already marked for this date")
}

func TestValidateNoTransactionRequest_Error_RetroactiveMarking(t *testing.T) {
	// Arrange
	service := &service{}
	today := time.Now()
	todayNormalized := time.Date(today.Year(), today.Month(), today.Day(), 0, 0, 0, 0, today.Location())
	twoDaysAgo := todayNormalized.AddDate(0, 0, -2)

	record := &financialsheet.Record{
		Points: financialsheet.Points{
			LastTransactionDate: todayNormalized, // Last transaction was today
		},
	}

	// Act - trying to mark two days ago
	err := service.validateNoTransactionRequest(record, twoDaysAgo)

	// Assert
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "cannot mark no transactions for dates before yesterday")
}

func TestValidateNoTransactionRequest_Error_SameDayAsRegularTransaction(t *testing.T) {
	// Arrange
	service := &service{}
	today := time.Now()
	todayNormalized := time.Date(today.Year(), today.Month(), today.Day(), 0, 0, 0, 0, today.Location())

	record := &financialsheet.Record{
		Points: financialsheet.Points{
			LastTransactionDate: todayNormalized, // Already had a transaction today
		},
	}

	// Act - trying to mark no transactions on the same day as a regular transaction
	err := service.validateNoTransactionRequest(record, todayNormalized)

	// Assert
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "cannot mark no transactions on the same day as a regular transaction")
}

func TestNoTransactions_RealWorldScenario_TransactionYesterdayNoTransactionToday(t *testing.T) {
	// Arrange
	ctx := context.Background()
	mockRepo := &MockRepository{}
	mockLeague := &MockLeagueService{}

	service := &service{
		Repository:    mockRepo,
		LeagueService: mockLeague,
	}

	// Simulate: User had a transaction yesterday and already has a streak of 1
	yesterday := time.Now().AddDate(0, 0, -1)
	yesterdayNormalized := time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 0, 0, 0, 0, yesterday.Location())

	record := &financialsheet.Record{
		ObjectID: primitive.NewObjectID(),
		UserID:   "user123",
		UserName: "Test User",
		Points: financialsheet.Points{
			Current:             1,                   // Already has 1 day streak from yesterday's transaction
			Best:                1,                   // Best is also 1
			LastTransactionDate: yesterdayNormalized, // Last transaction was yesterday
		},
		YearData: make(map[int]financialsheet.YearData),
		Balance:  monetary.Amount(0),
	}

	// Mock expectations
	mockRepo.On("Update", ctx, mock.AnythingOfType("*financialsheet.Record")).Return(nil)
	mockLeague.On("RecordTransactionForAllUserLeagues", ctx, "user123", mock.AnythingOfType("time.Time")).Return(nil)

	// Act - Call NoTransactions today
	err := service.NoTransactions(ctx, record, yesterday.Location())

	// Assert
	assert.NoError(t, err)
	assert.Equal(t, 2, record.Points.Current, "Streak should continue from 1 to 2") // This should be 2, not 1
	assert.Equal(t, 2, record.Points.Best, "Best should be updated to 2")
	assert.False(t, record.Points.LastTransactionDate.IsZero())
	assert.False(t, record.Points.LastNoTransactionDate.IsZero())
	assert.Len(t, record.Points.NoTransactionDates, 1)

	// Verify the date is today
	today := time.Now()
	todayNormalized := time.Date(today.Year(), today.Month(), today.Day(), 0, 0, 0, 0, today.Location())
	assert.True(t, record.Points.LastTransactionDate.Equal(todayNormalized), "LastTransactionDate should be today")

	mockRepo.AssertExpectations(t)
	mockLeague.AssertExpectations(t)
}

func TestNoTransactions_Error_SameDayAsRegularTransaction(t *testing.T) {
	// Arrange
	ctx := context.Background()
	service := &service{}

	today := time.Now()
	todayNormalized := time.Date(today.Year(), today.Month(), today.Day(), 0, 0, 0, 0, today.Location())

	record := &financialsheet.Record{
		ObjectID: primitive.NewObjectID(),
		UserID:   "user123",
		UserName: "Test User",
		Points: financialsheet.Points{
			Current:             1,
			Best:                1,
			LastTransactionDate: todayNormalized, // Already had a transaction today
		},
		YearData: make(map[int]financialsheet.YearData),
		Balance:  monetary.Amount(0),
	}

	// Act - Try to call NoTransactions on the same day as a regular transaction
	err := service.NoTransactions(ctx, record, today.Location())

	// Assert
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "cannot mark no transactions on the same day as a regular transaction")
}
